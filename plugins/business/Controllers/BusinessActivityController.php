<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\BusinessActivity;
use Plugins\Business\Models\BusinessComment;
use Plugins\Business\Models\BusinessActivityAttachment;

class BusinessActivityController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for each action
     */
    protected function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'store' => 'manage_businesses',
            'update' => 'manage_businesses',
            'destroy' => 'manage_businesses',
            'addComment' => 'view_businesses',
            'updateComment' => 'view_businesses',
            'deleteComment' => 'view_businesses',
            'uploadAttachment' => 'manage_businesses',
            'downloadAttachment' => 'view_businesses',
            'deleteAttachment' => 'manage_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Show the form for creating a new activity
     */
    public function create(Business $business): View
    {
        $messageTypes = [
            'comment' => 'Comment',
            'chat' => 'Chat Message',
            'email' => 'Email',
            'phone' => 'Phone Call',
            'meeting' => 'Meeting',
            'visit' => 'Site Visit',
            'whatsapp' => 'WhatsApp',
            'sms' => 'SMS',
            'reminder' => 'Reminder',
            'follow_up' => 'Follow Up'
        ];

        return view('plugins.business::activities.create', compact('business', 'messageTypes'));
    }

    /**
     * Display the specified activity
     */
    public function show(Business $business, BusinessActivity $activity): View
    {
        // Ensure activity belongs to business
        if ($activity->business_id !== $business->id) {
            abort(404);
        }

        $activity->load(['user', 'comments.user', 'attachments']);

        return view('plugins.business::activities.show', compact('business', 'activity'));
    }

    /**
     * Show the form for editing the specified activity
     */
    public function edit(Business $business, BusinessActivity $activity): View
    {
        // Ensure activity belongs to business
        if ($activity->business_id !== $business->id) {
            abort(404);
        }

        // Check if user can edit this activity
        if ($activity->user_id !== auth()->id() && !auth()->user()->hasPermission('manage_businesses')) {
            abort(403, 'You do not have permission to edit this activity.');
        }

        $messageTypes = [
            'comment' => 'Comment',
            'chat' => 'Chat Message',
            'email' => 'Email',
            'phone' => 'Phone Call',
            'meeting' => 'Meeting',
            'visit' => 'Site Visit',
            'whatsapp' => 'WhatsApp',
            'sms' => 'SMS',
            'reminder' => 'Reminder',
            'follow_up' => 'Follow Up'
        ];

        return view('plugins.business::activities.edit', compact('business', 'activity', 'messageTypes'));
    }

    /**
     * Get activities for a business (AJAX)
     */
    public function index(Request $request, Business $business): JsonResponse
    {
        $perPage = $request->get('per_page', 20);
        $page = $request->get('page', 1);
        $messageType = $request->get('message_type');
        $dateRange = $request->get('date_range');
        $userId = $request->get('user_id');
        $search = $request->get('search');
        $since = $request->get('since'); // For real-time updates

        $query = $business->activities()
            ->with(['user', 'comments.user', 'attachments'])
            ->visible()
            ->latest();

        // Apply message type filters
        if ($messageType) {
            switch ($messageType) {
                case 'status_change':
                case 'lead_to_deal':
                case 'deal_to_customer':
                case 'customer_to_partner':
                case 'churned':
                case 'lost':
                    $query->where('type', 'status_change');
                    if ($messageType !== 'status_change') {
                        $query->where('metadata->status_transition', $messageType);
                    }
                    break;
                case 'chat':
                case 'email':
                case 'phone':
                case 'meeting':
                case 'visit':
                case 'whatsapp':
                case 'sms':
                    $query->where('type', 'communication')
                          ->where('metadata->communication_type', $messageType);
                    break;
                case 'comment':
                    $query->where('type', 'comment');
                    break;
                case 'document':
                    $query->where('type', 'document_upload');
                    break;
                case 'system':
                    $query->where('is_system_generated', true);
                    break;
                default:
                    $query->where('type', $messageType);
            }
        }

        // Apply date range filters
        if ($dateRange) {
            switch ($dateRange) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', today()->subDay());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()]);
                    break;
                case 'custom':
                    if ($request->has('start_date') && $request->has('end_date')) {
                        $query->whereBetween('created_at', [
                            $request->get('start_date'),
                            $request->get('end_date')
                        ]);
                    }
                    break;
            }
        }

        // Apply user filter
        if ($userId) {
            if ($userId === 'system') {
                $query->where('is_system_generated', true);
            } else {
                $query->where('user_id', $userId);
            }
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($since) {
            $query->where('id', '>', $since);
        }

        $activities = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $activities->items(),
            'pagination' => [
                'current_page' => $activities->currentPage(),
                'last_page' => $activities->lastPage(),
                'per_page' => $activities->perPage(),
                'total' => $activities->total(),
                'has_more' => $activities->hasMorePages(),
            ]
        ]);
    }

    /**
     * Store a new manual activity
     */
    public function store(Request $request, Business $business)
    {
        $request->validate([
            'message_type' => 'required|string|in:comment,chat,email,phone,meeting,visit,whatsapp,sms,reminder,follow_up',
            'message' => 'required|string|max:1000',
            'reply_to_id' => 'nullable|integer|exists:business_activities,id',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max per file
        ]);

        // Determine activity type and category based on message type
        $typeMapping = [
            'comment' => ['type' => 'comment', 'category' => 'user_action'],
            'chat' => ['type' => 'communication', 'category' => 'communication'],
            'email' => ['type' => 'communication', 'category' => 'communication'],
            'phone' => ['type' => 'communication', 'category' => 'communication'],
            'meeting' => ['type' => 'communication', 'category' => 'communication'],
            'visit' => ['type' => 'communication', 'category' => 'communication'],
            'whatsapp' => ['type' => 'communication', 'category' => 'communication'],
            'sms' => ['type' => 'communication', 'category' => 'communication'],
            'reminder' => ['type' => 'reminder', 'category' => 'user_action'],
            'follow_up' => ['type' => 'follow_up', 'category' => 'user_action'],
        ];

        $mapping = $typeMapping[$request->message_type];

        // Prepare metadata
        $metadata = [];
        if (in_array($request->message_type, ['chat', 'email', 'phone', 'meeting', 'visit', 'whatsapp', 'sms'])) {
            $metadata['communication_type'] = $request->message_type;
        }

        if ($request->reply_to_id) {
            $metadata['reply_to_id'] = $request->reply_to_id;
        }

        $activity = BusinessActivity::createActivity([
            'business_id' => $business->id,
            'user_id' => auth()->id(),
            'type' => $mapping['type'],
            'category' => $mapping['category'],
            'severity' => 'info',
            'title' => $this->generateMessageTitle($request->message_type, $request->message),
            'description' => $request->message,
            'metadata' => $metadata,
            'is_system_generated' => false,
        ]);

        // Handle file attachments
        if ($request->hasFile('attachments')) {
            $this->handleAttachments($request->file('attachments'), $activity);
        }

        $activity->load(['user', 'comments.user', 'attachments']);

        // Handle AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully.',
                'activity' => $activity
            ]);
        }

        // Handle regular form submissions
        return redirect()->route('business.show', $business)
                        ->with('success', 'Activity added successfully.')
                        ->withFragment('activity');
    }

    /**
     * Update an activity (Edit message)
     */
    public function update(Request $request, Business $business, BusinessActivity $activity)
    {
        // Check if user can edit this activity
        if ($activity->user_id !== auth()->id() && !auth()->user()->hasPermission('manage_businesses')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to edit this message.'
            ], 403);
        }

        $request->validate([
            'message' => 'required|string|max:1000',
            'message_type' => 'nullable|string|in:comment,chat,email,phone,meeting,visit,whatsapp,sms,reminder,follow_up',
        ]);

        // Store original content for edit history
        $originalContent = $activity->description;
        $editHistory = $activity->metadata['edit_history'] ?? [];
        $editHistory[] = [
            'content' => $originalContent,
            'edited_at' => now()->toISOString(),
            'edited_by' => auth()->id(),
        ];

        // Update metadata
        $metadata = $activity->metadata ?? [];
        $metadata['edit_history'] = $editHistory;

        if ($request->message_type && $request->message_type !== $activity->type) {
            $metadata['communication_type'] = $request->message_type;
        }

        $activity->update([
            'title' => $this->generateMessageTitle($request->message_type ?? 'comment', $request->message),
            'description' => $request->message,
            'metadata' => $metadata,
            'edited_at' => now(),
        ]);

        // Handle AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Activity updated successfully.',
                'activity' => $activity
            ]);
        }

        // Handle regular form submissions
        return redirect()->route('business.activities.show', [$business, $activity])
                        ->with('success', 'Activity updated successfully.');
    }

    /**
     * Delete an activity (Delete message)
     */
    public function destroy(Business $business, BusinessActivity $activity)
    {
        // Check if user can delete this activity
        if ($activity->user_id !== auth()->id() && !auth()->user()->hasPermission('manage_businesses')) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to delete this message.'
                ], 403);
            }
            abort(403, 'You do not have permission to delete this activity.');
        }

        // Store activity info for logging
        $activityInfo = [
            'id' => $activity->id,
            'title' => $activity->title,
            'type' => $activity->type,
            'user_name' => $activity->user->name ?? 'Unknown',
        ];

        // Delete associated attachments
        if ($activity->attachments) {
            foreach ($activity->attachments as $attachment) {
                // Delete file from storage
                if (Storage::disk('local')->exists($attachment->file_path)) {
                    Storage::disk('local')->delete($attachment->file_path);
                }
                $attachment->delete();
            }
        }

        // Soft delete the activity
        $activity->delete();

        // Log the deletion
        BusinessActivity::createActivity([
            'business_id' => $business->id,
            'user_id' => auth()->id(),
            'type' => 'system',
            'category' => 'system',
            'severity' => 'info',
            'title' => 'Message Deleted',
            'description' => "Message '{$activityInfo['title']}' by {$activityInfo['user_name']} was deleted",
            'metadata' => ['deleted_activity' => $activityInfo],
            'is_system_generated' => true,
        ]);

        // Handle AJAX requests
        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Message deleted successfully.'
            ]);
        }

        // Handle regular form submissions
        return redirect()->route('business.show', $business)
                        ->with('success', 'Activity deleted successfully.')
                        ->withFragment('activity');
    }

    /**
     * Add a comment to an activity
     */
    public function addComment(Request $request, Business $business, BusinessActivity $activity): JsonResponse
    {
        $request->validate([
            'content' => 'required|string',
        ]);

        $processedContent = BusinessComment::processContent($request->content);

        $comment = BusinessComment::create([
            'business_id' => $business->id,
            'user_id' => auth()->id(),
            'activity_id' => $activity->id,
            'content' => $processedContent['content'],
            'content_html' => $processedContent['content_html'],
            'mentions' => $processedContent['mentions'],
        ]);

        $comment->load('user');

        return response()->json([
            'success' => true,
            'message' => 'Comment added successfully.',
            'comment' => $comment
        ]);
    }

    /**
     * Update a comment
     */
    public function updateComment(Request $request, Business $business, BusinessComment $comment): JsonResponse
    {
        // Check if user can edit this comment
        if (!$comment->canEdit(auth()->user())) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to edit this comment.'
            ], 403);
        }

        $request->validate([
            'content' => 'required|string',
        ]);

        $processedContent = BusinessComment::processContent($request->content);

        $comment->update([
            'content' => $processedContent['content'],
            'content_html' => $processedContent['content_html'],
            'mentions' => $processedContent['mentions'],
        ]);

        $comment->markAsEdited();

        return response()->json([
            'success' => true,
            'message' => 'Comment updated successfully.',
            'comment' => $comment
        ]);
    }

    /**
     * Delete a comment
     */
    public function deleteComment(Business $business, BusinessComment $comment): JsonResponse
    {
        // Check if user can delete this comment
        if (!$comment->canDelete(auth()->user())) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to delete this comment.'
            ], 403);
        }

        $comment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Comment deleted successfully.'
        ]);
    }

    /**
     * Upload attachment to an activity
     */
    public function uploadAttachment(Request $request, Business $business, BusinessActivity $activity): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'is_public' => 'boolean',
        ]);

        try {
            $attachment = BusinessActivityAttachment::createFromUpload(
                $request->file('file'),
                $activity,
                [
                    'is_public' => $request->boolean('is_public', false),
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully.',
                'attachment' => $attachment
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download an attachment
     */
    public function downloadAttachment(Business $business, BusinessActivityAttachment $attachment)
    {
        // Check if user can access this attachment
        if (!$attachment->canAccess(auth()->user())) {
            abort(403, 'You do not have permission to access this file.');
        }

        $attachment->incrementDownloadCount();

        return response()->download(
            storage_path('app/' . $attachment->file_path),
            $attachment->original_filename
        );
    }

    /**
     * Delete an attachment
     */
    public function deleteAttachment(Business $business, BusinessActivityAttachment $attachment): JsonResponse
    {
        // Check if user can delete this attachment
        if ($attachment->uploaded_by !== auth()->id() && !auth()->user()->hasPermission('manage_businesses')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to delete this attachment.'
            ], 403);
        }

        $attachment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Attachment deleted successfully.'
        ]);
    }

    /**
     * Generate a title for the message based on type and content
     */
    private function generateMessageTitle(string $messageType, string $message): string
    {
        $titles = [
            'comment' => 'Comment',
            'chat' => 'Chat Message',
            'email' => 'Email Communication',
            'phone' => 'Phone Call',
            'meeting' => 'Meeting',
            'visit' => 'Site Visit',
            'whatsapp' => 'WhatsApp Message',
            'sms' => 'SMS Message',
            'reminder' => 'Reminder',
            'follow_up' => 'Follow-up',
        ];

        $title = $titles[$messageType] ?? 'Message';

        // Add preview of message content
        $preview = Str::limit($message, 50);
        return "{$title}: {$preview}";
    }

    /**
     * Handle file attachments for an activity
     */
    private function handleAttachments(array $files, BusinessActivity $activity): void
    {
        foreach ($files as $file) {
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $fileName = Str::uuid() . '.' . $extension;
            $filePath = "business-activities/{$activity->business_id}/{$activity->id}/" . $fileName;

            // Store file
            $file->storeAs('', $filePath, 'local');

            // Create attachment record (assuming you have an attachments table)
            $activity->attachments()->create([
                'file_name' => $fileName,
                'original_name' => $originalName,
                'file_path' => $filePath,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'uploaded_by' => auth()->id(),
            ]);
        }
    }
}
